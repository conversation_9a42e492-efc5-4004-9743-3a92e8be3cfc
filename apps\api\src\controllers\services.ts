import { Request, Response } from 'express';
import { supabaseAdmin } from '@freela/database/src/supabase';
import { logger } from '../utils/logger';
import { AppError } from '../utils/errors';

// Service data mapping utilities
const mapToSupabaseService = (data: any) => {
  const mapped: any = {};
  
  for (const [key, value] of Object.entries(data)) {
    switch (key) {
      case 'expertId':
        mapped.expert_id = value;
        break;
      case 'priceType':
        mapped.price_type = value;
        break;
      case 'basePrice':
        mapped.base_price = value;
        break;
      case 'deliveryTime':
        mapped.delivery_time = value;
        break;
      case 'serviceType':
        mapped.service_type = value;
        break;
      case 'isActive':
        mapped.status = value ? 'ACTIVE' : 'PAUSED';
        break;
      case 'createdAt':
        mapped.created_at = value;
        break;
      case 'updatedAt':
        mapped.updated_at = value;
        break;
      default:
        mapped[key] = value;
    }
  }
  
  return mapped;
};

const mapFromSupabaseService = (data: any) => {
  if (!data) return data;
  
  const mapped: any = {};
  
  for (const [key, value] of Object.entries(data)) {
    switch (key) {
      case 'expert_id':
        mapped.expertId = value;
        break;
      case 'price_type':
        mapped.priceType = value;
        break;
      case 'base_price':
        mapped.basePrice = value;
        break;
      case 'delivery_time':
        mapped.deliveryTime = value;
        break;
      case 'service_type':
        mapped.serviceType = value;
        break;
      case 'status':
        mapped.isActive = value === 'ACTIVE';
        break;
      case 'created_at':
        mapped.createdAt = value;
        break;
      case 'updated_at':
        mapped.updatedAt = value;
        break;
      default:
        mapped[key] = value;
    }
  }
  
  return mapped;
};

/**
 * Create a new service
 */
export const createService = async (req: Request, res: Response) => {
  try {
    const userId = req.user?.id;
    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // Check if user has an expert profile
    const { data: expertProfile, error: expertError } = await supabaseAdmin
      .from('expert_profiles')
      .select('id')
      .eq('user_id', userId)
      .single();

    if (expertError || !expertProfile) {
      throw new AppError('Expert profile not found. Please complete your expert profile first.', 400);
    }

    const serviceData = {
      ...req.body,
      expertId: expertProfile.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    };

    const supabaseData = mapToSupabaseService(serviceData);

    const { data: service, error } = await supabaseAdmin
      .from('services')
      .insert(supabaseData)
      .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `)
      .single();

    if (error) {
      logger.error('Error creating service:', error);
      throw new AppError('Failed to create service', 500);
    }

    const mappedService = mapFromSupabaseService(service);

    logger.info('Service created successfully', { 
      serviceId: service.id, 
      expertId: expertProfile.id,
      userId 
    });

    res.status(201).json({
      success: true,
      message: 'Service created successfully',
      data: mappedService
    });
  } catch (error) {
    logger.error('Create service error:', error);
    
    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Search and list services with filters and pagination
 */
export const searchServices = async (req: Request, res: Response) => {
  try {
    const {
      q,
      category,
      subcategory,
      serviceType,
      priceType,
      minPrice,
      maxPrice,
      governorate,
      city,
      tags,
      sortBy = 'created_at',
      sortOrder = 'desc',
      page = 1,
      limit = 20,
      expertId,
      isActive = true
    } = req.query;

    let query = supabaseAdmin
      .from('services')
      .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          bio,
          skills,
          rating,
          review_count,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `, { count: 'exact' });

    // Apply filters
    if (isActive !== undefined) {
      if (isActive === 'true' || isActive === true) {
        query = query.eq('status', 'ACTIVE');
      } else {
        query = query.neq('status', 'ACTIVE');
      }
    }

    if (category) {
      query = query.eq('category', category);
    }

    if (subcategory) {
      query = query.eq('subcategory', subcategory);
    }

    if (serviceType) {
      query = query.eq('service_type', serviceType);
    }

    if (priceType) {
      query = query.eq('price_type', priceType);
    }

    if (minPrice) {
      query = query.gte('base_price', minPrice);
    }

    if (maxPrice) {
      query = query.lte('base_price', maxPrice);
    }

    if (expertId) {
      query = query.eq('expert_id', expertId);
    }

    // Location filters for physical services
    if (governorate) {
      query = query.contains('location', { governorate });
    }

    if (city) {
      query = query.contains('location', { city });
    }

    // Text search
    if (q) {
      query = query.or(`title.ilike.%${q}%,description.ilike.%${q}%,category.ilike.%${q}%`);
    }

    // Tags search
    if (tags) {
      const tagArray = tags.toString().split(',').map(tag => tag.trim());
      query = query.overlaps('tags', tagArray);
    }

    // Sorting
    const sortField = sortBy === 'price' ? 'base_price' :
                     sortBy === 'rating' ? 'expert_profiles.rating' :
                     sortBy === 'popularity' ? 'expert_profiles.review_count' :
                     'created_at';
    
    query = query.order(sortField, { ascending: sortOrder === 'asc' });

    // Pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query.range(offset, offset + Number(limit) - 1);

    const { data: services, error, count } = await query;

    if (error) {
      logger.error('Error searching services:', error);
      throw new AppError('Failed to search services', 500);
    }

    const mappedServices = services?.map(mapFromSupabaseService) || [];

    const totalPages = Math.ceil((count || 0) / Number(limit));

    res.json({
      success: true,
      message: 'Services retrieved successfully',
      data: {
        services: mappedServices,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: count || 0,
          totalPages
        }
      }
    });
  } catch (error) {
    logger.error('Search services error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Get service by ID
 */
export const getServiceById = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;

    const { data: service, error } = await supabaseAdmin
      .from('services')
      .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          bio,
          skills,
          rating,
          review_count,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `)
      .eq('id', id)
      .single();

    if (error || !service) {
      throw new AppError('Service not found', 404);
    }

    const mappedService = mapFromSupabaseService(service);

    res.json({
      success: true,
      message: 'Service retrieved successfully',
      data: mappedService
    });
  } catch (error) {
    logger.error('Get service by ID error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Update service
 */
export const updateService = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // Get the service to check ownership
    const { data: existingService, error: fetchError } = await supabaseAdmin
      .from('services')
      .select(`
        *,
        expert_profiles!inner(
          id,
          user_id
        )
      `)
      .eq('id', id)
      .single();

    if (fetchError || !existingService) {
      throw new AppError('Service not found', 404);
    }

    // Check if user owns the service or is admin
    if (userRole !== 'ADMIN' && existingService.expert_profiles.user_id !== userId) {
      throw new AppError('Access denied - you can only update your own services', 403);
    }

    const updateData = {
      ...req.body,
      updatedAt: new Date().toISOString()
    };

    const supabaseData = mapToSupabaseService(updateData);

    const { data: updatedService, error } = await supabaseAdmin
      .from('services')
      .update(supabaseData)
      .eq('id', id)
      .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `)
      .single();

    if (error) {
      logger.error('Error updating service:', error);
      throw new AppError('Failed to update service', 500);
    }

    const mappedService = mapFromSupabaseService(updatedService);

    logger.info('Service updated successfully', {
      serviceId: id,
      userId
    });

    res.json({
      success: true,
      message: 'Service updated successfully',
      data: mappedService
    });
  } catch (error) {
    logger.error('Update service error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Delete service
 */
export const deleteService = async (req: Request, res: Response) => {
  try {
    const { id } = req.params;
    const userId = req.user?.id;
    const userRole = req.user?.role;

    if (!userId) {
      throw new AppError('User not authenticated', 401);
    }

    // Get the service to check ownership
    const { data: existingService, error: fetchError } = await supabaseAdmin
      .from('services')
      .select(`
        *,
        expert_profiles!inner(
          id,
          user_id
        )
      `)
      .eq('id', id)
      .single();

    if (fetchError || !existingService) {
      throw new AppError('Service not found', 404);
    }

    // Check if user owns the service or is admin
    if (userRole !== 'ADMIN' && existingService.expert_profiles.user_id !== userId) {
      throw new AppError('Access denied - you can only delete your own services', 403);
    }

    // Check if service has active bookings
    const { data: activeBookings, error: bookingError } = await supabaseAdmin
      .from('bookings')
      .select('id')
      .eq('service_id', id)
      .in('status', ['PENDING', 'ACCEPTED', 'IN_PROGRESS']);

    if (bookingError) {
      logger.error('Error checking active bookings:', bookingError);
    }

    if (activeBookings && activeBookings.length > 0) {
      throw new AppError('Cannot delete service with active bookings. Please complete or cancel active bookings first.', 400);
    }

    const { error } = await supabaseAdmin
      .from('services')
      .delete()
      .eq('id', id);

    if (error) {
      logger.error('Error deleting service:', error);
      throw new AppError('Failed to delete service', 500);
    }

    logger.info('Service deleted successfully', {
      serviceId: id,
      userId
    });

    res.json({
      success: true,
      message: 'Service deleted successfully'
    });
  } catch (error) {
    logger.error('Delete service error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};

/**
 * Get services by expert ID
 */
export const getServicesByExpert = async (req: Request, res: Response) => {
  try {
    const { expertId } = req.params;
    const {
      isActive,
      page = 1,
      limit = 20
    } = req.query;

    let query = supabaseAdmin
      .from('services')
      .select(`
        *,
        expert_profiles!inner(
          id,
          user_id,
          bio,
          skills,
          rating,
          review_count,
          users!inner(
            id,
            first_name,
            last_name,
            email
          )
        )
      `, { count: 'exact' })
      .eq('expert_id', expertId);

    // Apply active filter if specified
    if (isActive !== undefined) {
      if (isActive === 'true' || isActive === true) {
        query = query.eq('status', 'ACTIVE');
      } else {
        query = query.neq('status', 'ACTIVE');
      }
    }

    // Pagination
    const offset = (Number(page) - 1) * Number(limit);
    query = query.range(offset, offset + Number(limit) - 1);

    // Order by creation date (newest first)
    query = query.order('created_at', { ascending: false });

    const { data: services, error, count } = await query;

    if (error) {
      logger.error('Error getting services by expert:', error);
      throw new AppError('Failed to retrieve expert services', 500);
    }

    const mappedServices = services?.map(mapFromSupabaseService) || [];

    const totalPages = Math.ceil((count || 0) / Number(limit));

    res.json({
      success: true,
      message: 'Expert services retrieved successfully',
      data: {
        services: mappedServices,
        pagination: {
          page: Number(page),
          limit: Number(limit),
          total: count || 0,
          totalPages
        }
      }
    });
  } catch (error) {
    logger.error('Get services by expert error:', error);

    if (error instanceof AppError) {
      return res.status(error.statusCode).json({
        success: false,
        message: error.message,
        type: error.type
      });
    }

    res.status(500).json({
      success: false,
      message: 'Internal server error',
      type: 'INTERNAL_ERROR'
    });
  }
};
